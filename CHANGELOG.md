# Changelog

All notable changes to the Career Ireland API project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [0.8.2] - 2025-07-19

### Added
- **Comprehensive README.md**: Complete overhaul with detailed setup instructions, API documentation, developer guide, and database management procedures
  - **Project Setup**: Prerequisites, environment configuration, database setup, and development server instructions
  - **API Documentation**: Complete endpoint reference with examples for all modules (auth, user, admin, agent, mentor, payment, documents, packages, etc.)
  - **Developer Guide**: Code architecture overview, development workflow, best practices, and debugging instructions
  - **Database Management**: Backup procedures, migration best practices, schema modification guidelines, and monitoring commands
  - **Production Deployment**: PM2 configuration, environment setup, monitoring, and logging procedures
  - **Essential Links**: Documentation references, external resources, and architecture guides

### Improved
- **Codebase Cleanup**: Comprehensive audit and removal of unused files to improve maintainability
  - **Scripts Directory**: Removed 11 unused script files (migration scripts, test files, validation scripts)
  - **Documentation**: Removed 39 outdated documentation files while preserving essential guides
  - **Test Runner**: Updated `scripts/run-all-tests.ts` to reference only existing and functional test scripts
  - **Build Process**: Verified successful TypeScript compilation and server startup after cleanup

### Fixed
- **Build Verification**: Confirmed successful compilation and server startup with all modules loading correctly
  - **TypeScript Compilation**: All files compile without errors
  - **Server Initialization**: All modules, routes, and services initialize properly
  - **Database Connections**: PostgreSQL and Supabase connections established successfully
  - **Authentication**: JWT authentication system working correctly

### Verified
- **Database Schema Integrity**: Confirmed `notification_queue` and `notification_template` tables are actively used and essential
  - **Active Usage**: Found in `src/application/services/notification.service.ts`, tests, and seeding scripts
  - **Core Functionality**: Supports email notification queue management, retry logic, and analytics
  - **Decision**: Tables preserved as they are critical for notification system functionality

### Technical Improvements
- **Documentation Standards**: Established comprehensive documentation structure following industry best practices
- **Code Organization**: Improved codebase structure by removing unused files and updating references
- **Development Workflow**: Enhanced developer onboarding with detailed setup and debugging instructions
- **Production Readiness**: Documented deployment procedures, monitoring, and maintenance practices

## [0.8.1] - 2025-07-19

### Fixed
- **Missing Document Reminder System**: Fixed critical scheduling logic issue
  - **Bug Fix**: Changed reminder logic from `>=` to `===` to send reminders only on exact day intervals instead of every day after threshold
  - **Configuration Update**: Updated default reminder days from 10 to 6 days to match current configuration
  - **Timing Logic**: Reminders now sent exactly every 6 days (or user-configured interval) instead of daily after 6 days
  - **Prevents Spam**: Eliminates daily reminder emails that were being sent after the initial reminder threshold

### Improved
- **PM2 Production Configuration**: Enhanced PM2 ecosystem configuration for better production deployment
  - **Fixed Hard-coded Paths**: Removed hard-coded `/root/api` paths for better portability
  - **Enhanced Error Handling**: Added proper restart policies, memory limits, and error logging
  - **Compiled JavaScript**: Updated scheduler to use compiled JavaScript instead of TypeScript in production
  - **Log Management**: Added structured logging with separate error, output, and combined log files
  - **Process Monitoring**: Improved process monitoring with better restart policies and memory management

### Removed
- **Broken Test File**: Removed `scripts/test-scheduler-timing.ts` that was causing TypeScript compilation errors
  - **Build Fix**: Resolved TypeScript compilation issues preventing successful builds
  - **Clean Codebase**: Eliminated unused test file with broken imports

### Technical Improvements
- **Build Verification**: Confirmed successful TypeScript compilation and server startup
- **API Functionality**: Verified all API endpoints are working correctly
- **Database Schema**: Confirmed notification template tables are actively used and should be retained
- **Configuration Consistency**: Aligned all default values to use 6-day reminder intervals

## [0.8.0] - 2025-07-18

### Major Changes
- **Unified Notification Configuration System**: Migrated from individual user notification files to a single unified configuration approach
  - **Breaking Change**: NotificationSettingsStorageService now uses unified `config/notification-settings.json` instead of individual user files
  - **Architecture Update**: Simplified notification settings management with centralized configuration
  - **File Structure**: Eliminated individual user notification files in favor of unified system defaults

### Added
- **Unified Configuration File**: Created `config/notification-settings.json` with system-wide default notification settings
  - Includes `user_id` field for tracking configuration updates
  - Provides default settings for all notification types
  - Supports centralized notification preference management

### Changed
- **NotificationSettingsStorageService**: Complete refactor to support unified configuration approach
  - `readSettings()`: Now reads from unified config file and returns user-specific settings
  - `writeSettings()`: Updates system defaults in unified configuration
  - `updateSettings()`: Merges updates with existing unified settings
  - `settingsExist()`: Checks for unified configuration file existence
  - `deleteSettings()`: Simplified for unified config approach
- **File Management**: Cleaned up config directory by removing obsolete individual user notification files
- **Documentation**: Updated all references to reflect new unified configuration approach

### Improved
- **System Architecture**: Simplified notification settings management with unified configuration
- **File Organization**: Cleaner config directory structure with single notification configuration file
- **Maintainability**: Reduced complexity by eliminating individual user file management
- **Performance**: Improved efficiency with single file reads instead of multiple individual files

### Technical Details
- **Configuration Storage**: Single JSON file approach for notification settings
- **User Settings**: Individual user preferences handled in memory with fallback to system defaults
- **Backward Compatibility**: Service interface remains the same, only internal implementation changed
- **Error Handling**: Enhanced error logging for unified configuration file operations
- **Testing**: Updated test suites to work with unified configuration approach

### Migration Notes
- **Existing Deployments**: Individual user notification files will be ignored in favor of unified configuration
- **Default Behavior**: All users will use system default notification settings from unified config
- **Customization**: User-specific preferences can be handled at the application layer
- **File Cleanup**: Old individual user notification files can be safely removed

## [0.7.1] - 2025-07-18

### Changed
- **File Management and Organization**: Streamlined notification configuration and document reminder system
  - **Notification Settings**: Created unified `config/notification-settings.json` file with `user_id` field for tracking configuration updates
  - **Config Directory Cleanup**: Removed obsolete notification configuration files to maintain clean directory structure
  - **Script Renaming**: Renamed `scripts/consolidated-document-reminder.ts` to `scripts/document-reminder.ts` for simplified naming convention
  - **Reference Updates**: Updated all import statements, npm scripts, and documentation references to reflect new filename

### Updated
- **Package Scripts**: Updated npm scripts to use new `document-reminder.ts` filename
  - Changed: `reminder:documents`, `reminder:documents:scheduler`, `reminder:documents:status`
  - Changed: `test:document-reminder`, `test:document-reminder:watch`, `test:document-reminder:cov`
- **Ecosystem Configuration**: Updated PM2 ecosystem configuration to use renamed script file
- **Documentation**: Updated README files and inline documentation to reflect new naming conventions
- **Test Files**: Renamed and updated test files to match new script naming

### Improved
- **Code Quality**: Enhanced file-based error logging already implemented in document reminder system
- **Build Verification**: Confirmed TypeScript compilation and development server startup work correctly after changes
- **Maintainability**: Simplified file structure and naming for better developer experience

### Technical Details
- **File Structure**: Maintained existing functionality while improving organization
- **Backward Compatibility**: All existing functionality preserved during renaming process
- **Error Handling**: Comprehensive file-based logging system already in place for debugging
- **Testing**: All existing unit tests updated to work with new file structure

## [0.7.0] - 2025-07-18

### Changed
- **BREAKING CHANGE: Notification Settings Storage Structure**: Refactored notification settings from nested JSON structure to flat structure for improved simplicity and maintainability
  - **Old Structure**: Single file with user IDs as top-level keys: `{"user_id": {"id": "...", "user_id": "...", ...}}`
  - **New Structure**: Individual files per user with flat JSON objects: `config/user_id.json` containing `{"id": "...", "user_id": "...", ...}`
  - **Migration Script**: Created `scripts/migrate-notification-settings.ts` to automatically convert existing data with backup functionality
  - **Rollback Support**: Migration script includes rollback capability to restore from backup if needed

### Added
- **Notification Settings Migration Tool**: Comprehensive migration system for converting notification settings data structure
  - **Automatic Backup**: Creates timestamped backup of original data before migration
  - **Data Validation**: Validates data integrity during migration process
  - **Error Recovery**: Handles migration errors gracefully with detailed error reporting
  - **CLI Interface**: User-friendly command-line interface with help and rollback options
  - **Package Script**: Added `npm run migrate:notification-settings` for easy execution

### Improved
- **NotificationSettingsStorageService**: Completely refactored to work with individual files per user
  - **File-based Storage**: Each user's settings stored in separate JSON file for better isolation
  - **Error Handling**: Enhanced error handling with file-based logging for debugging
  - **Data Integrity**: Improved validation and structure checking for notification settings
  - **Performance**: Reduced memory usage by loading only required user settings instead of all settings

### Removed
- **Obsolete Reminder Files**: Cleaned up legacy reminder system files to maintain only the consolidated system
  - Removed: `scripts/missing-document-reminder.ts`
  - Removed: `scripts/missing-document-reminder.service.ts`
  - Removed: `scripts/scheduler/missing-document-scheduler.ts`
  - Removed: `scripts/scheduler-manager.ts`
  - Removed: `scripts/utils/process-manager.ts`
  - Removed: `scripts/utils/reminder-logger.ts`
  - Removed: Associated test files and documentation for obsolete systems
- **Package Scripts**: Removed obsolete npm scripts related to old reminder system
  - Removed: `script:missing-document-reminder*` scripts
  - Removed: `scheduler:*` scripts
  - Removed: `test:missing-document-reminder*` scripts

### Updated
- **Package Version**: Updated to 0.7.0 following semantic versioning for minor version with breaking changes
- **Ecosystem Configuration**: Updated `ecosystem.config.js` to use consolidated document reminder script
- **Documentation**: Updated system documentation to reflect new notification settings structure

### Technical Details
- **File Structure**: Notification settings now stored as individual JSON files in `config/` directory
- **API Compatibility**: PUT endpoints continue to merge data without deleting existing fields as per user preference
- **Simple Implementation**: Maintains user preference for minimal, clean code without over-engineering
- **Error Logging**: Continues to use file-based error logging for debugging as preferred by user

## [0.6.0] - 2025-07-18

### Added
- **Unified Notification Configuration**: Created centralized `config/notification-settings.json` with default system settings and user tracking
- **Document Reminder System**: Created a single, self-contained document reminder system that automatically sends email notifications to users about missing required documents
  - **Single Script Implementation**: Consolidated all missing document reminder functionality into one script (`scripts/consolidated-document-reminder.ts`) eliminating the need for multiple files and complex command structures
  - **Array Operators for Database Queries**: Implemented proper array operators (has, hasSome, hasEvery) instead of equality operators for filtering applications with missing documents
  - **React Email Template Integration**: Integrated with existing `application-requirements.tsx` template pattern using React email rendering with fallback templates for error scenarios
  - **File-based JSON Configuration**: Uses `config/notification-settings.json` to read `missing_document_reminder_days` values for configurable reminder intervals (currently set to 10 days)
  - **7-Day Completion Timeline**: Includes clear messaging in emails that users have 7 days to complete the application process once all required documents are uploaded
  - **Self-contained Scheduling**: Implements internal scheduling mechanism within the application rather than requiring external cron jobs for easier deployment and management across different environments
  - **Non-blocking Email Sending**: Implements asynchronous email sending with proper error handling and fallback templates
  - **File-based Error Logging**: Comprehensive logging system that writes to `logs/document-reminders/` directory with JSON-formatted log entries for debugging
  - **Comprehensive Unit Tests**: Full test coverage including database queries, date logic, email functionality, error scenarios, and edge cases

### Improved
- **Email Template Consistency**: Enhanced email templates to follow consistent header/footer structure from existing templates with proper branding and user-friendly messaging
- **Database Query Performance**: Optimized database queries to use proper Prisma array operators for better performance and accuracy when filtering applications with missing documents
- **Error Handling**: Implemented robust error handling with fallback mechanisms for email template rendering failures and database connection issues
- **Memory Management**: Designed with minimal, clean code approach without over-engineering to prevent memory leaks and ensure efficient resource usage

### Changed
- **Package Scripts**: Added new npm scripts for the consolidated reminder system:
  - `npm run reminder:documents` - Execute one-time reminder process
  - `npm run reminder:documents -- --scheduler` - Run as long-running scheduler process
  - `npm run reminder:documents -- --status` - Check scheduler status
  - `npm run test:consolidated-reminder` - Run unit tests for the consolidated system
- **Package Version**: Updated to 0.6.0 following semantic versioning for new feature addition

### Technical Features
- **Environment Variable Configuration**: Supports scheduler configuration through environment variables (SCHEDULER_ENABLED, SCHEDULER_HOUR, SCHEDULER_MINUTE, etc.)
- **Graceful Shutdown**: Implements proper signal handling for SIGTERM/SIGINT for clean scheduler shutdown
- **Dynamic Import**: Uses dynamic imports for React email templates to avoid TypeScript compilation issues
- **Cross-platform Compatibility**: Works on both Windows and Unix-like systems with proper path handling

### Quality Assurance
- **TypeScript Compilation**: Verified TypeScript compilation compatibility with existing codebase
- **Development Server**: Confirmed functionality with `npm run start:dev`
- **Unit Test Coverage**: Comprehensive test suite covering all major functionality including database operations, email sending, scheduler functionality, and error scenarios
- **Error Scenario Testing**: Extensive testing of edge cases including missing user data, email service failures, and database connection issues

## [0.5.1] - 2025-07-18

### Fixed
- **TypeScript Build Configuration**: Fixed TypeScript compilation issues by excluding test files from production build
  - **Build Process**: Updated tsconfig.json to exclude test/**/* and *.spec.ts files from compilation
  - **Production Build**: Ensured clean production build without test-related TypeScript errors
  - **Module Resolution**: Fixed import path issues that were preventing successful compilation

### Improved
- **PM2 Ecosystem Configuration**: Enhanced and tested PM2 configuration for production deployment
  - **API Service**: Verified main API service starts correctly with PM2 using dist/src/main.js
  - **Scheduler Service**: Fixed scheduler service configuration to use direct ts-node execution instead of npm scripts
  - **Process Management**: Tested both services start successfully and remain online
  - **Production Ready**: Configured proper working directories and environment variables for server deployment
  - **Cross-Platform**: Ensured PM2 configuration works on both development and production environments

### Changed
- **Package Version**: Updated package version to 0.5.1 following semantic versioning for bug fixes
- **Build Exclusions**: Modified TypeScript configuration to properly separate production code from test code

## [0.5.0] - 2025-07-18

### Fixed
- **Scheduler Service Cross-Platform Compatibility**: Fixed scheduler service to work properly on Windows and Unix-like systems
  - **Process Manager Enhancement**: Updated ProcessManager to use `npx ts-node` on Windows for better compatibility
  - **Path Handling**: Fixed Windows-specific path handling issues in process start time detection
  - **Shell Integration**: Added proper shell integration for Windows process spawning
  - **Error Handling**: Improved error handling for process management across different operating systems

### Added
- **Production Process Management**: Added PM2 ecosystem configuration for production deployment
  - **Ecosystem Configuration**: Created `ecosystem.config.js` with simple, production-ready PM2 configuration
  - **Admin Service**: Configured main API service as "admin" process with proper memory limits and restart policies
  - **Scheduler Service**: Configured scheduler as separate PM2 process with dedicated environment variables
  - **Environment Variables**: Production-ready environment configuration for scheduler service
  - **Auto-restart**: Configured automatic restart policies for both services

### Improved
- **Unit Test Coverage**: Enhanced and fixed comprehensive unit tests for scheduler functionality
  - **Logger Service Mocking**: Fixed LoggerService mock interfaces to match actual service methods
  - **Date Mocking**: Improved Date constructor mocking for better test reliability
  - **Module Resolution**: Fixed TypeScript module resolution issues for React email templates
  - **Jest Configuration**: Updated Jest configuration to handle .tsx files and proper module mapping
  - **Cross-Platform Tests**: Ensured tests work correctly on both Windows and Unix-like systems

### Changed
- **File-Based Error Logging**: Enhanced file-based error logging with improved cross-platform compatibility
- **TypeScript Compilation**: Verified and ensured all TypeScript compilation issues are resolved
- **Package Version**: Updated package version to 0.5.0 following semantic versioning

## [0.4.0] - 2025-07-14

### Added
- **Missing Document Reminder System (v1.0.0)**: Comprehensive self-contained scheduling system for automated document reminder emails
  - **Self-Contained Scheduler**: Built-in scheduler that runs reminder processes at specified intervals without requiring external cron setup
  - **Environment-Based Configuration**: Flexible configuration system supporting development, staging, and production environments with customizable schedules
  - **Process Management**: Complete start/stop/restart capabilities for long-running scheduler processes with health monitoring and automatic restart
  - **Smart Timing Logic**: Sends reminders only when exact number of configured days has passed since last update (NOT daily reminders)
  - **User-Configurable Frequencies**: Each user can set their own reminder frequency (7, 14, 21 days, etc.) via notification settings
  - **30-Day Cutoff**: Automatically stops sending reminders for applications older than 30 days to prevent spam
  - **Database Query Service**: Efficient queries for applications with pending/rejected/missing documents using proper array operators
  - **React Email Template**: Professional missing document reminder template with document categorization and status badges
  - **Email Integration**: Non-blocking email sending with fallback templates and comprehensive error recovery
  - **File-Based Logging**: Dedicated logging system with log rotation, separate error logs, and performance tracking
  - **CLI Management Tools**: Command-line interface for scheduler management (start/stop/restart/status)
  - **Comprehensive Testing**: Complete unit test suite covering database queries, email integration, scheduler logic, and error handling
  - **Documentation**: Detailed README with usage examples, configuration options, and troubleshooting guides

### Added
- **Email Template Integration System (v2.2.0)**: Complete email template integration with application workflow management
  - **Email Template Integration Service**: New dedicated service (`EmailTemplateIntegrationService`) for managing all email template operations with comprehensive error handling and fallback templates
  - **Application Workflow Integration**: Seamless integration of email templates into application creation, status updates, document management, and workflow template assignment flows
  - **Document Request Email Integration**: Automated email sending when documents are specifically requested from applicants with deadline tracking and clear instructions
  - **Application Requirements Email Integration**: Automatic email sending upon application creation and workflow template assignment with required documents list and 7-day completion deadline
  - **Status Change Email Integration**: Dynamic email notifications for application status updates with color-coded status indicators, next steps guidance, and contextual messaging
  - **Document Rejection Email Integration**: Professional email notifications for document rejections with specific feedback, resubmission instructions, and helpful guidelines
  - **Comprehensive Error Handling**: File-based error logging with fallback email templates ensuring email delivery even when React templates fail
  - **Unit Test Suite**: Complete test coverage with 15 test cases covering all email integration scenarios, error handling, and fallback mechanisms
  - **Environment Configuration**: Automatic environment variable integration for website URLs, support emails, and service configuration
- **Email Template System Enhancement (v1.1.0)**: Added four new professional email templates for application workflow management
  - **Document Request Template** (`document-request.tsx`): Professional template for requesting document submissions with clear instructions and deadline information
  - **Application Requirements Template** (`application-requirements.tsx`): Automated template for new applications with required documents list, 7-day completion deadline, and website login link
  - **Application Status Change Template** (`application-status-change.tsx`): Dynamic template for status updates with color-coded status indicators and next steps guidance
  - **Document Rejection Template** (`document-rejection.tsx`): Supportive template for document rejections with specific feedback, resubmission instructions, and helpful guidelines
  - **Consistent Design System**: All templates follow the same header/footer structure as existing `purchase-notification.tsx` template
  - **TypeScript Type Safety**: Comprehensive TypeScript interfaces for all template props with proper validation
  - **Responsive Design**: Mobile-friendly templates with consistent styling and branding
  - **Environment Integration**: Templates automatically use WEBSITE environment variable for login links
  - **Comprehensive Testing**: Added unit test suite with 8 test cases covering template imports and TypeScript validation
  - **Jest Configuration**: Created dedicated test configuration for template testing with proper TSX support
- **Agent Auto-Assignment and Removal System (v0.1.0)**: Implemented comprehensive agent management for applications with role-based auto-assignment
  - **Auto-Assignment Logic**: Agent users are automatically assigned to applications they create, while admin users remain unassigned
  - **Agent Removal Endpoint**: New DELETE /applications/:applicationId/agents/:agentId endpoint for removing agents from applications
  - **Role-Based Authorization**: Agents can only remove themselves, admins can remove any agent from applications
  - **Idempotent Operations**: Agent removal handles non-existent agent IDs gracefully without errors
  - **Comprehensive Testing**: Added extensive unit tests covering all auto-assignment and removal scenarios
  - **Enhanced API Documentation**: Updated Swagger documentation with detailed examples and error codes
  - **Backward Compatibility**: Preserves existing agent_ids array functionality and API response formats
- **File-Based Notification Settings Storage (v2.1)**: Implemented comprehensive file-based storage system for notification settings
  - **NotificationSettingsStorageService**: New service for JSON file operations with atomic writes and file locking
  - **Atomic File Operations**: Implemented atomic write operations to prevent data corruption during concurrent access
  - **File Locking Mechanisms**: Added proper file locking for safe concurrent access scenarios
  - **Comprehensive Error Handling**: Enhanced error logging and user-friendly error messages for file I/O operations
  - **Unit Tests**: Added comprehensive test suite for file-based notification settings functionality
  - **Config Directory Structure**: Created `config/notification-settings/` directory for JSON storage

### Changed
- **BREAKING**: Notification settings now stored in JSON files instead of database tables
- **NotificationService Refactoring**: Updated to use file-based storage for all notification settings operations
- **PUT Endpoint Behavior**: PUT operations now preserve existing fields during updates (merge behavior)
- **Enhanced Logging**: Improved error handling and logging for notification settings operations
- **Module Dependencies**: Updated ApplicationModule and DocumentModule to include NotificationSettingsStorageService

### Removed
- **Database Table**: Removed `notification_settings` table from Prisma schema
- **Database Migration**: Created migration to drop notification_settings table and related constraints
- **Prisma Relationships**: Removed notification_settings relationship from user model
- **Database Dependencies**: Eliminated all database-related code for notification settings

### Technical Implementation Details
- **JSON File Storage**: Each user's notification settings stored as individual JSON files (`{user_id}.json`)
- **Atomic Operations**: Implemented temporary file writes with atomic renames to prevent corruption
- **Backup and Recovery**: Automatic backup creation during updates with rollback capability
- **Concurrent Access Safety**: File locking map prevents race conditions during simultaneous operations
- **Default Settings**: Automatic creation of default settings for new users
- **Data Validation**: Input validation for notification preference ranges (e.g., reminder days 1-365)

### Migration and Compatibility
- **API Compatibility**: Maintained existing API interface - no changes to endpoint signatures
- **Backward Compatibility**: Same response format and behavior for all notification settings endpoints
- **Data Migration**: Existing database records should be migrated to JSON files before deployment
- **Build Verification**: Confirmed successful TypeScript compilation and development server startup

- **Application Status Management Enhancement (v2.0)**: Implemented comprehensive application status logic for payment webhook and direct creation endpoints
  - **Payment Webhook Applications**: Applications created via payment webhook (`createApplicationFromPayment`) now have `status: ApplicationStatus.Pending`
  - **Direct POST Applications**: Applications created via POST /applications endpoint (`createNewApplication`) now have `status: ApplicationStatus.Pending`
  - **ApplicationStatus Enum**: Restored "Pending" status to the ApplicationStatus enum in Prisma schema (prisma/schema/schema.prisma)
  - **Database Migration**: Updated database schema with `npx prisma db push` to include "Pending" status in ApplicationStatus enum
  - **Service Layer Updates**: Modified ApplicationService methods to use proper enum values instead of string literals
  - **Comprehensive Testing**: Added dedicated test suite (`test/application/application-status.spec.ts`) with 8 comprehensive test cases covering:
    - Payment webhook application creation with Pending status
    - Guest payment webhook application creation with Pending status
    - Existing application detection and handling
    - Direct POST endpoint application creation with Pending status
    - Payment ID validation and error handling
    - Workflow template validation and error handling
    - Optional agent assignment handling
    - ApplicationStatus enum consistency verification
  - **Error Handling**: Maintained existing error handling patterns while updating status logic with proper TypeScript typing
  - **Backward Compatibility**: Existing application creation flows remain unchanged except for status setting
  - **Build Verification**: Confirmed successful compilation with `npm run build` and server startup with `npm start`
- **Notification Settings Management System**: Complete user notification preferences management with database-backed settings and comprehensive validation
  - **Database Schema Enhancement**:
    - **notification_settings Table**: Added user_id field with unique constraint and foreign key relationship to user table
    - **Migration Update**: Fixed existing migration to include user_id column in table creation
    - **User Relationship**: Added notification_settings relationship to user model with cascade deletion
    - **Proper Indexing**: Added indexes for user_id and created_at fields for optimal query performance
  - **API Endpoints**:
    - **GET /notifications/settings**: Retrieve current user notification preferences with automatic default creation if none exist
    - **PUT /notifications/settings**: Update user notification preferences with partial update support and comprehensive validation
    - **JWT Authentication**: All endpoints require valid JWT authentication for user-specific settings access
    - **Swagger Documentation**: Complete API documentation with examples for partial and full updates
  - **Service Layer Implementation**:
    - **NotificationService**: Enhanced with getUserNotificationSettings, updateUserNotificationSettings, and createDefaultSettings methods
    - **User-Specific Queries**: Fixed findFirst() calls to use proper user filtering with findUnique() for user-specific data
    - **Default Settings Creation**: Automatic creation of default notification settings for new users without requiring user data initially
    - **Atomic Transactions**: All database operations wrapped in Prisma transactions for data consistency and rollback support
    - **shouldReceiveNotification Method**: Enhanced to check user-specific preferences with fail-safe default behavior
  - **Validation and Error Handling**:
    - **DTO Validation**: NotificationSettingsDto and UpdateNotificationSettingsDto with comprehensive class-validator decorators
    - **Day Range Validation**: missing_document_reminder_days field validates positive integers between 1-365 days with custom error messages
    - **Boolean Field Validation**: All notification preference flags validated as proper boolean values
    - **Service-Level Validation**: Additional validation in service layer for day ranges with user-friendly error messages
    - **Controller Error Handling**: Enhanced error handling for validation errors, database errors, and generic failures
    - **User-Friendly Messages**: All error responses include descriptive messages for better user experience
  - **Testing Infrastructure**:
    - **Unit Tests**: Comprehensive test suite for NotificationService covering all methods and edge cases
    - **Controller Tests**: Complete test coverage for NotificationController including authentication and error scenarios
    - **DTO Validation Tests**: Dedicated tests for validation logic including boundary conditions and error messages
    - **Mock Integration**: Proper mocking of PrismaService, LoggerService, and MailerService for isolated testing
    - **Edge Case Coverage**: Tests for undefined values, mixed updates, validation failures, and database errors
  - **Build and Development Verification**:
    - **TypeScript Compilation**: Verified successful compilation with npm run build (0 errors)
    - **Development Server**: Confirmed successful startup with npm run start:dev
    - **Route Registration**: Verified proper registration of /notifications/settings endpoints (GET and PUT)
    - **Memory Leak Prevention**: Implemented proper resource cleanup and error handling patterns

- **Application Workflow Template Assignment System**: New endpoint for reassigning workflow templates to applications with atomic cleanup
  - **POST /applications/assign-workflow-template**: New admin-only endpoint for changing workflow templates on existing applications
  - **Atomic Transaction Support**: Complete cleanup of old form data, document records, and storage files when reassigning templates
  - **Comprehensive Validation**: Validates application existence, workflow template compatibility, service type matching, and active status
  - **Storage Cleanup Integration**: Automatic cleanup of orphaned files from Supabase storage during template reassignment
  - **Enhanced Error Handling**: User-friendly error messages for all validation scenarios and edge cases

- **Application List Workflow Template Enhancement**: Enhanced GET /applications endpoint with complete workflow template data
  - **ApplicationListItem Interface**: Added optional workflow_template field with id, name, and description
  - **ApplicationTransformerService**: Enhanced transformApplicationListItem method to include workflow template data
  - **Error Recovery**: Comprehensive error handling with safe fallback responses for malformed data
  - **Unit Test Coverage**: Added comprehensive test suite covering applications with/without workflow templates
  - **Backward Compatibility**: All existing API consumers remain unaffected by the enhancement

### Enhanced
- **POST /applications Endpoint**: Enhanced application creation with automatic agent assignment
  - **Auto-Assignment Logic**: Applications created by agent users automatically include the creating agent in agent_ids array
  - **Admin Preservation**: Applications created by admin users maintain empty agent_ids array (no auto-assignment)
  - **Merge Behavior**: Preserves any existing agent_ids from request body while adding creating agent if not present
  - **Duplicate Prevention**: Prevents duplicate agent IDs when creating agent is already in assigned_agent array
  - **Enhanced Service Method**: Updated createNewApplication to accept userTokenType parameter for role-based logic
  - **Comprehensive Logging**: Added detailed logging for auto-assignment operations and admin application creation

- **DELETE /applications/:applicationId/agents/:agentId Endpoint**: New endpoint for agent removal from applications
  - **Role-Based Access Control**: Admin users can remove any agent, agent users can only remove themselves
  - **Comprehensive Validation**: Validates both application and agent existence before removal
  - **Idempotent Operations**: Gracefully handles cases where agent is not in agent_ids array
  - **Error Handling**: Returns appropriate HTTP status codes (200, 403, 404) with descriptive messages
  - **Service Integration**: New removeAgentFromApplication method in ApplicationService with proper error handling
  - **Authorization Guards**: Uses JwtAdminOrAgent guard with role-specific authorization logic

### Technical Implementation Details
- **Service Layer Enhancement**: Modified ApplicationService.createNewApplication method signature to include userTokenType parameter
- **Controller Updates**: Enhanced ApplicationController.createApplication to pass user.tokenType to service layer
- **Database Operations**: Utilizes existing agent_ids array field in application table for multi-agent support
- **Role Detection**: Uses JWT payload tokenType field to distinguish between 'agent', 'admin', and other user types
- **Array Management**: Implements proper array manipulation for adding/removing agent IDs with duplicate prevention
- **Transaction Safety**: All database operations maintain existing transaction patterns and error handling
- **GET /applications Endpoint**:
  - **service_id Field**: Added service_id to application response payload for better service identification
  - **workflow_template_name Field**: Added workflow_template_name to response payload showing the name of the assigned workflow template
  - **workflow_template Object**: Enhanced ApplicationListItem interface to include complete workflow template data (id, name, description)
  - **Comprehensive Workflow Data**: Applications now return full workflow template information in list view for better frontend integration
  - **Error Handling**: Added graceful error recovery for malformed application data with safe fallback responses
  - **Backward Compatibility**: All existing fields preserved while adding new response fields
  - **Enhanced Response Structure**: Improved application data structure with workflow template name resolution

- **Media Service**:
  - **File Deletion Support**: Added `deleteFile` and `deleteMultipleFiles` methods for Supabase storage cleanup
  - **Batch Operations**: Support for deleting multiple files with detailed success/failure reporting
  - **Error Resilience**: Graceful handling of storage service unavailability and partial failures
  - **Connection Testing**: Pre-deletion connection validation to ensure storage service availability

### Fixed
- **Workflow Template Assignment Unique Constraint Error**: Fixed database constraint violation in `/applications/assign-workflow-template` endpoint
  - **Root Cause**: Multiple assignment attempts created duplicate `(application_id, document_vault_id)` entries with empty document_vault_id values
  - **Solution**: Implemented proper `document_vault` record creation before `application_document` records to satisfy unique constraints
  - **Database Pattern**: Following established pattern from `application-document.service.ts` for temporary document vault creation
  - **Error Handling**: Enhanced error messages for constraint violations and improved user-friendly feedback
  - **Validation**: Added workflow template structure validation to prevent invalid template assignments
  - **Transaction Safety**: Maintained atomic transaction behavior with proper rollback on failures

### Technical Improvements
- **Notification Settings Infrastructure**: Complete notification preferences management system with database integration and comprehensive testing
  - **Database Schema Enhancements**:
    - **notification_settings Table**: Added user_id field with unique constraint and proper foreign key relationship
    - **Migration Fixes**: Updated existing migration to include user_id column in table creation SQL
    - **User Model Integration**: Added notification_settings relationship to user model with cascade deletion
    - **Indexing Strategy**: Implemented proper indexes for user_id and created_at fields for optimal query performance
  - **Service Layer Architecture**:
    - **NotificationService Enhancement**: Extended with getUserNotificationSettings, updateUserNotificationSettings, and createDefaultSettings methods
    - **User-Specific Data Access**: Fixed findFirst() calls to use proper user filtering with findUnique() for accurate data retrieval
    - **Default Settings Logic**: Implemented automatic creation of default notification settings without requiring initial user data
    - **Transaction Management**: All database operations wrapped in Prisma transactions for consistency and rollback support
    - **Validation Layer**: Added service-level validation for day ranges (1-365) with user-friendly error messages
  - **API Layer Implementation**:
    - **NotificationController**: New controller with proper JWT authentication, error handling, and comprehensive API documentation
    - **DTO Architecture**: Added NotificationSettingsDto and UpdateNotificationSettingsDto with class-validator decorators
    - **Validation Rules**: Implemented positive integer validation for day ranges and boolean validation for notification flags
    - **Error Handling**: Enhanced error handling for validation errors, database errors, and authentication failures
    - **Swagger Integration**: Complete API documentation with request/response examples and error scenarios
  - **Module Integration**: Updated ApplicationModule to include NotificationController with proper dependency injection and service registration
  - **Testing Infrastructure**:
    - **Unit Test Coverage**: Comprehensive test suite for NotificationService covering all methods, edge cases, and error scenarios
    - **Controller Testing**: Complete test coverage for NotificationController including authentication, validation, and error handling
    - **DTO Validation Tests**: Dedicated tests for validation logic including boundary conditions and custom error messages
    - **Mock Integration**: Proper mocking of PrismaService, LoggerService, and MailerService for isolated unit testing
    - **Edge Case Testing**: Tests for undefined values, mixed updates, validation failures, and database connection errors

- **Database Transaction Management**: Enhanced atomic operations for complex workflow template reassignment with rollback support
- **Service Layer Enhancement**: Updated ApplicationService with comprehensive workflow template assignment logic
- **DTOs Enhancement**: Added `AssignWorkflowTemplateDto` and `AssignWorkflowTemplateResponseDto` with proper validation
- **Storage Integration**: Improved Supabase storage integration with deletion capabilities and error handling
- **Response Transformation**: Enhanced application response transformation to include workflow template names

### Testing
- **Comprehensive Test Coverage**: Added extensive unit tests for workflow template assignment functionality
  - **Success Scenarios**: Tests for successful template assignment with cleanup tracking
  - **Error Scenarios**: Tests for all validation failures and edge cases
  - **Storage Cleanup**: Tests for storage cleanup success and failure scenarios
  - **Transaction Rollback**: Tests for database transaction rollback on failures
  - **Enhanced Response Tests**: Tests for new response fields in GET applications endpoint
  - **Document Vault Creation**: Tests for proper document_vault record creation during template assignment
  - **Unique Constraint Prevention**: Tests to ensure duplicate constraint errors are prevented
  - **Workflow Template Validation**: Tests for invalid and empty workflow template structures

### API Documentation
- **Notification Settings Endpoints**: Complete API documentation for user notification preferences management
  - **GET /notifications/settings**: Retrieve user notification preferences with automatic default creation
    - **Authentication**: Requires valid JWT token (Bearer authentication)
    - **HTTP Method**: GET
    - **Endpoint**: `/notifications/settings`
    - **Response Schema**:
      ```json
      {
        "agent_assigned": boolean,
        "case_status_update": boolean,
        "agent_query": boolean,
        "document_rejection": boolean,
        "missing_document_reminder_days": number (1-365),
        "system_maintenance": boolean,
        "upcoming_deadline_alerts": boolean,
        "final_decision_issued": boolean
      }
      ```
    - **Default Values**: All boolean fields default to `true`, reminder days default to `7`
    - **Auto-Creation**: Creates default settings if none exist for the user
    - **Response Codes**:
      - `200`: Settings retrieved successfully
      - `401`: Unauthorized - Invalid or missing JWT token
      - `500`: Internal server error

  - **PUT /notifications/settings**: Update user notification preferences with partial update support
    - **Authentication**: Requires valid JWT token (Bearer authentication)
    - **HTTP Method**: PUT
    - **Endpoint**: `/notifications/settings`
    - **Request Schema**: All fields optional for partial updates
      ```json
      {
        "agent_assigned"?: boolean,
        "case_status_update"?: boolean,
        "agent_query"?: boolean,
        "document_rejection"?: boolean,
        "missing_document_reminder_days"?: number (1-365),
        "system_maintenance"?: boolean,
        "upcoming_deadline_alerts"?: boolean,
        "final_decision_issued"?: boolean
      }
      ```
    - **Validation Rules**:
      - `missing_document_reminder_days`: Must be integer between 1-365 (inclusive)
      - All boolean fields: Must be valid boolean values (true/false)
      - At least one field must be provided in the request body
    - **Response Schema**: Same as GET endpoint with updated values
    - **Response Codes**:
      - `200`: Settings updated successfully
      - `400`: Bad request - Validation failed or no fields provided
      - `401`: Unauthorized - Invalid or missing JWT token
      - `500`: Internal server error
    - **Example Requests**:
      - **Partial Update**:
        ```json
        {
          "agent_assigned": false,
          "missing_document_reminder_days": 14
        }
        ```
      - **Full Update**:
        ```json
        {
          "agent_assigned": true,
          "case_status_update": true,
          "agent_query": true,
          "document_rejection": true,
          "missing_document_reminder_days": 7,
          "system_maintenance": true,
          "upcoming_deadline_alerts": true,
          "final_decision_issued": true
        }
        ```
    - **Error Responses**:
      - **Validation Error**:
        ```json
        {
          "success": false,
          "message": "Invalid notification settings provided",
          "error": "Missing document reminder days must be between 1 and 365 days"
        }
        ```
      - **No Fields Error**:
        ```json
        {
          "success": false,
          "message": "At least one notification setting field must be provided",
          "error": "Validation failed"
        }
        ```
      }
      ```
    - **Validation Rules**:
      - `missing_document_reminder_days`: Must be integer between 1-365 days
      - At least one field must be provided for update
      - Boolean fields accept only `true` or `false` values
    - **Response**: Returns updated complete settings object
    - **Error Responses**:
      - `400`: Invalid input data or validation errors
      - `401`: Unauthorized - missing or invalid JWT token
      - `500`: Internal server error

### Security & Validation
- **JWT Authentication**: All notification settings endpoints require valid JWT authentication for user-specific access
- **Input Validation**: Comprehensive validation for all notification preference fields with detailed error messages
- **User Isolation**: Settings are strictly user-specific with no cross-user access possible
- **Data Integrity**: Atomic database transactions ensure consistency during updates
- **Admin-Only Access**: Workflow template assignment restricted to admin users only
- **Service Type Validation**: Ensures workflow templates match application service types
- **Active Template Validation**: Prevents assignment of inactive workflow templates
- **Duplicate Assignment Prevention**: Validates against assigning the same template twice
- **Atomic Operations**: All database changes wrapped in transactions to prevent data inconsistency

## [4.14.0] - 2025-07-09

### Added
- **Workflow Template Default System**: Enhanced workflow template management with default template functionality
  - **Default Template Logic**: Added `isDefault` boolean field to workflow_template table with business rules ensuring only one default template per service
  - **Package Name Integration**: Enhanced GET /workflow-templates endpoint to include package name in responses for package service types
  - **PUT /workflow-templates/:id/default**: New endpoint for setting/unsetting default status with proper validation and atomic operations
  - **Database Migration**: Added migration `20250709132909_add_is_default_field_to_workflow_template` with proper indexing for performance

### Enhanced
- **Workflow Template Service**:
  - **Atomic Default Management**: Implemented transaction-based logic to ensure only one default template per service using Prisma transactions
  - **Package Name Resolution**: Added automatic package name lookup for package service types in response mapping
  - **Enhanced Create/Update**: Updated create and update methods to handle isDefault field with proper validation
  - **Backward Compatibility**: All existing functionality preserved while adding new default template capabilities

### Technical Improvements
- **Database Schema**: Added `isDefault` boolean field with default false, proper indexing on serviceType, serviceId, and isDefault combinations
- **Service Layer**: Enhanced WorkflowTemplateService with `setDefault` method and `ensureOnlyOneDefault` private method for atomic operations
- **DTOs Enhancement**: Added `SetDefaultTemplateDto` and updated existing DTOs to support isDefault field
- **Response Enhancement**: Updated `WorkflowTemplateResponseDto` to include isDefault and packageName fields

### API Enhancements
- **PUT /workflow-templates/:id/default**: Set workflow template as default for its service with automatic conflict resolution
- **Enhanced GET Responses**: All workflow template endpoints now include isDefault status and package name (when applicable)
- **Validation Logic**: Comprehensive validation ensures data integrity and prevents multiple default templates per service

### Testing and Quality Assurance
- **Comprehensive Unit Tests**: Added extensive test coverage for default template functionality including edge cases
- **Build Verification**: Confirmed zero TypeScript compilation errors with `npm run build`
- **Development Server**: Verified successful startup with all new routes properly mapped
- **Database Migration**: Successfully applied migration with proper rollback capabilities

## [4.13.1] - 2025-07-09

### Enhanced
- **GET /applications Mobile Number Support**: Enhanced application list responses to include user mobile numbers
  - **Complete User Contact Info**: Added `mobile` field to user objects in application responses when available
  - **Backward Compatible**: Mobile field is optional and gracefully handles null/undefined values
  - **Proper Field Mapping**: Fixed field mapping from database `mobileNo` to response `mobile` for consistency
  - **Type Safety**: Updated interfaces and DTOs maintain proper TypeScript typing for optional mobile field

### Technical Improvements
- **Application Service Enhancement**: Updated Prisma select statement to include `mobileNo` field from users table
- **Transformer Service Fix**: Corrected field mapping from `application.user.mobileNo` to `mobile` in response transformation
- **Comprehensive Testing**: Added specific test cases for mobile number handling including null/undefined scenarios
- **Response Structure**: Maintains existing response structure while adding complete user contact information

### Testing and Quality Assurance
- **Mobile Number Tests**: Added dedicated test cases for mobile number inclusion and null handling
- **Build Verification**: Confirmed zero TypeScript compilation errors with `npm run build`
- **Development Server**: Verified successful startup with all routes properly mapped
- **Backward Compatibility**: Ensured existing response fields remain unchanged

## [4.13.0] - 2025-07-09

### Enhanced
- **GET /applications Endpoint Response Structure**: Enhanced application list responses to include comprehensive agent details and service names
  - **Agent Details Array**: Added `agent_ids` field containing full agent information (id, name, email) for all assigned agents
  - **Service Name Resolution**: Added `service_name` field that resolves service type and service ID to actual service names
  - **Backward Compatible**: Maintains all existing response fields while adding new enhanced data
  - **Performance Optimized**: Efficient bulk agent fetching with single database query and in-memory mapping
  - **Type Safety**: Updated DTOs and interfaces with proper TypeScript typing for new response structure

### Technical Improvements
- **Application Service Enhancement**: Updated `getApplicationsWithTransformation` method with agent details resolution
  - **Bulk Agent Fetching**: Collects all unique agent IDs across applications and fetches in single query
  - **Service Name Resolution**: Integrates existing `resolveServiceName` method for consistent service naming
  - **Error Handling**: Graceful fallback to original data if enhancement fails, ensuring reliability
  - **Memory Efficient**: Uses Map-based agent lookup for O(1) agent detail resolution
- **Transformer Service Updates**: Enhanced `ApplicationTransformerService` for new response structure
  - **Updated Interface**: Modified `ApplicationListItem` interface to include `agent_ids` array
  - **Response Transformation**: Updated transformation logic to use enhanced application data
  - **Backward Compatibility**: Maintains existing `assigned_agent` field for legacy support
- **DTO Enhancements**: Created comprehensive DTOs for improved API documentation
  - **ApplicationAgentDto**: Dedicated DTO for agent information in responses
  - **ApplicationListItemDto**: Updated list item DTO with new fields and proper Swagger documentation
  - **Type Safety**: Replaced generic `any[]` types with specific typed arrays

### Testing and Quality Assurance
- **Comprehensive Unit Tests**: Created dedicated test suite for enhanced response structure
  - **Response Structure Validation**: Tests verify presence and structure of new `agent_ids` and `service_name` fields
  - **Edge Case Handling**: Tests for applications with no assigned agents and various data scenarios
  - **Backward Compatibility**: Tests ensure existing response fields remain intact
  - **Service Integration**: Tests verify correct service method calls with proper parameters
- **Build Verification**: Confirmed zero TypeScript compilation errors with `npm run build`
- **Development Server**: Verified successful startup with `npm run start:dev` - all endpoints functional
- **Test Coverage**: All 5 new tests passing (100% success rate) with comprehensive scenario coverage

### API Response Changes
**Before Enhancement:**
```json
{
  "data": [{
    "id": "app_123",
    "application_number": "IMM-2024-000001",
    "service_type": "immigration",
    "status": "Under_Review",
    "assigned_agent": { "id": "agent_1", "name": "Agent Smith", "email": "<EMAIL>" }
  }]
}
```

**After Enhancement:**
```json
{
  "data": [{
    "id": "app_123",
    "application_number": "IMM-2024-000001",
    "service_type": "immigration",
    "service_name": "Work Permit Application",
    "status": "Under_Review",
    "agent_ids": [
      { "id": "agent_1", "name": "Agent Smith", "email": "<EMAIL>" },
      { "id": "agent_2", "name": "Agent Jones", "email": "<EMAIL>" }
    ],
    "assigned_agent": { "id": "agent_1", "name": "Agent Smith", "email": "<EMAIL>" }
  }]
}
```

### Migration Notes
- **No Breaking Changes**: All existing response fields remain unchanged
- **New Fields**: `agent_ids` array and `service_name` string added to all application list responses
- **Client Updates**: Frontend applications can immediately start using new fields without breaking existing functionality
- **Performance**: No impact on response times due to optimized bulk fetching strategy

## [4.12.0] - 2025-07-08

### 🚨 BREAKING CHANGES
- **Workflow Template API Consolidation**: Removed redundant `GET /workflow-templates/service-type/:serviceType` endpoint
  - **Migration Required**: Replace usage of `/workflow-templates/service-type/{serviceType}` with `/workflow-templates?serviceType={serviceType}&isActive=true`
  - **Equivalent Functionality**: The main endpoint provides the same filtering capability with additional flexibility
  - **Response Format Change**: Main endpoint returns paginated response instead of direct array

### Removed
- **Redundant Workflow Template Endpoint**: Cleaned up duplicate API functionality
  - Removed `GET /workflow-templates/service-type/:serviceType` endpoint from WorkflowTemplateController
  - Removed `findByServiceType()` method from WorkflowTemplateService
  - Removed corresponding service interface method and related imports
  - Cleaned up unused ServiceType imports and dependencies

### Enhanced
- **Consolidated API Architecture**: Streamlined workflow template access through unified endpoint
  - Enhanced main `GET /workflow-templates` endpoint provides equivalent serviceType filtering
  - Improved API consistency by consolidating filtering functionality into single endpoint
  - Maintained backward compatibility for all other workflow template operations
  - Preserved admin authentication requirements for all protected endpoints

### Technical Improvements
- **Code Quality and Maintainability**: Reduced code duplication and improved API design
  - Removed redundant controller method and service logic
  - Simplified workflow template interface by removing duplicate functionality
  - Updated test suites to reflect consolidated endpoint architecture
  - Verified removed endpoint returns proper 404 responses

- **Build and Development Verification**: Ensured all changes compile and run successfully
  - Verified TypeScript compilation with npm run build - zero compilation errors
  - Confirmed development server startup with npm run start:dev - all endpoints mapped correctly
  - Validated removed endpoint no longer appears in route mappings
  - Tested main endpoint provides equivalent serviceType filtering functionality

### Migration Guide
**For API Consumers Using the Removed Endpoint:**

**Before (Removed):**
```
GET /workflow-templates/service-type/immigration
Authorization: Bearer {admin-token}

Response: WorkflowTemplateResponseDto[]
```

**After (Use This Instead):**
```
GET /workflow-templates?serviceType=immigration&isActive=true
Authorization: Bearer {admin-token}

Response: PaginatedWorkflowTemplateResponseDto {
  data: WorkflowTemplateResponseDto[],
  total: number,
  page: number,
  limit: number,
  totalPages: number
}
```

**Code Migration Example:**
```typescript
// OLD - Will return 404
const templates = await fetch('/workflow-templates/service-type/immigration');

// NEW - Use this instead
const response = await fetch('/workflow-templates?serviceType=immigration&isActive=true');
const { data: templates } = await response.json();
```

## [4.11.0] - 2025-07-08

### Added
- **Immigration Service Visibility Management**: Enhanced immigration service administration with granular visibility control
  - Added new PATCH /immigration/:id/visibility endpoint for admin-only visibility management
  - Created UpdateVisibilityDto with proper validation for website_visible boolean field
  - Implemented admin authentication guard for visibility management endpoint
  - Added comprehensive unit and integration tests for visibility management functionality

- **Immigration Module Test Suite**: Comprehensive testing infrastructure for immigration services
  - Created complete test suite with controller, service, and integration tests
  - Added Jest configuration specifically for immigration module testing
  - Implemented 25 test cases covering all CRUD operations and visibility management
  - Added npm scripts for running immigration tests: test:immigration, test:immigration:watch, test:immigration:cov

### Enhanced
- **Workflow Template Flexibility**: Removed restrictions to support multiple workflow templates per product
  - Modified WorkflowTemplateService to allow multiple active workflow templates for the same service
  - Removed duplicate checking logic that previously prevented multiple workflows per immigration package
  - Enhanced workflow template creation and update processes to support flexible workflow management
  - Updated error messages and validation logic to reflect new multiple-workflow capability

- **Immigration Service Public Access**: Improved public API filtering for website visibility
  - Enhanced GET /immigration endpoint to filter results based on website_visible field
  - Maintained backward compatibility while adding new visibility filtering functionality
  - Ensured only website-visible immigration services are returned to public API consumers

### Removed
- **Immigration Admin Endpoint**: Cleaned up redundant administrative access points
  - Removed GET /immigration/admin endpoint and associated service method
  - Eliminated getAllForAdmin() method from ImmigrationService
  - Cleaned up unused imports and dependencies related to admin-only access
  - Simplified immigration service architecture by removing duplicate functionality

### Changed
- **Workflow Template Service Architecture**: Streamlined service logic for better maintainability
  - Removed checkServiceDuplicate() method and related duplicate checking logic
  - Simplified create() and update() methods by removing service-based duplicate validation
  - Updated workflow template creation to focus on template uniqueness rather than service restrictions
  - Enhanced code maintainability by removing complex duplicate checking workflows

### Technical Improvements
- **Build and Development Verification**: Ensured all changes compile and run successfully
  - Verified TypeScript compilation with npm run build - zero compilation errors
  - Confirmed development server startup with npm run start:dev - all endpoints mapped correctly
  - Validated new immigration visibility endpoint routing: /immigration/:immigrationId/visibility
  - Tested admin authentication requirements for all protected endpoints

- **Code Quality and Testing**: Maintained high code quality standards with comprehensive testing
  - Added 20+ unit tests for immigration controller and service functionality
  - Created integration tests for authentication, authorization, and endpoint behavior
  - Implemented proper mocking for PrismaService and JwtService dependencies
  - Verified test coverage for all new immigration service functionality
  - Added proper error handling and validation for all new endpoints

## [4.10.0] - 2025-07-08

### Added
- **Workflow Template Service ID Filtering**: Enhanced workflow template management with flexible service ID filtering
  - Added serviceId parameter to WorkflowTemplateFiltersDto with proper validation and API documentation
  - Created new GET /workflow-templates/service/:serviceId endpoint for direct service-based template retrieval
  - Implemented intelligent service ID validation: validates immigration services exist, allows other service types without validation
  - Added comprehensive unit tests for service ID filtering functionality with 100% coverage of edge cases
  - Created integration tests to verify endpoint behavior with real database queries and proper error handling

### Enhanced
- **Workflow Template Query Capabilities**: Improved workflow template filtering and retrieval options
  - Enhanced GET /workflow-templates endpoint to support serviceId query parameter for flexible filtering
  - Modified WorkflowTemplateService.findAll() to handle service ID filtering with proper validation logic
  - Added WorkflowTemplateService.findByServiceId() method for direct service-based template retrieval
  - Implemented backward compatibility for existing filtering while adding new service ID capabilities
  - Updated API documentation with comprehensive Swagger annotations for new serviceId parameter

### Changed
- **API Parameter Naming**: Standardized parameter naming from immigrationProductId to serviceId for better consistency
  - Renamed immigrationProductId to serviceId in WorkflowTemplateFiltersDto for broader applicability
  - Updated all API documentation to reflect the more generic serviceId parameter naming
  - Modified endpoint from /workflow-templates/immigration-product/:id to /workflow-templates/service/:serviceId
  - Enhanced parameter description to clarify usage for different service types (immigration, training, packages, consulting)
  - Maintained full backward compatibility while improving parameter semantics

### Technical Improvements
- **Service Validation Logic**: Implemented intelligent service validation based on service type
  - Added conditional validation: immigration services validated against immigration_service table
  - Non-immigration service types (training, packages, consulting) filtered by serviceId without external validation
  - Enhanced error handling with user-friendly messages for non-existent immigration services
  - Implemented proper logging for service ID filtering operations with detailed context information
  - Added comprehensive test coverage for all service validation scenarios and error conditions

- **Code Quality and Testing**: Maintained comprehensive testing and build verification
  - Verified TypeScript compilation with npm run build - zero errors after implementation
  - Confirmed development server startup with npm run dev - all new routes mapped successfully
  - Added 8 new unit tests covering service ID filtering functionality with edge cases
  - Created 4 integration tests verifying endpoint behavior with real database operations
  - Maintained 100% test coverage for new functionality while preserving existing test suite integrity

## [4.9.0] - 2025-07-08

### Added
- **Immigration Service Website Visibility Control**: Enhanced immigration service management with website visibility flag
  - Added website_visible boolean field to immigration_service table with default value of true
  - Created database migration (20250708000001_add_website_visible_flag_to_immigration_service) for seamless schema update
  - Added website_visible field to ImmigrationDto with proper validation for admin control
  - Implemented admin-only GET /immigration/admin endpoint to retrieve all immigration services regardless of visibility
  - Enhanced public GET /immigration endpoint to filter services based on website_visible flag for improved content management

### Enhanced
- **Payment System Source-Based Processing**: Modified payment endpoint to support source-specific behavior for immigration services
  - Added optional source parameter to CreateMultiMethodPaymentDto for payment source identification
  - Enhanced /v2/payment endpoint to skip Stripe integration when source is "immigration" while maintaining Stripe for other sources
  - Implemented conditional Stripe processing: immigration source payments save data only, other sources continue with full Stripe integration
  - Added comprehensive logging for immigration payments that bypass Stripe integration with detailed context information
  - Updated API documentation to reflect new source parameter and immigration-specific payment behavior

- **Immigration Service Management Enhancement**: Improved immigration service administration and public access control
  - Modified ImmigrationService.getAll() to filter results based on website_visible flag for public API consumption
  - Added ImmigrationService.getAllForAdmin() method for administrative access to all services regardless of visibility
  - Enhanced immigration service DTO with optional website_visible field for flexible service management
  - Maintained backward compatibility while adding new visibility control features

### Changed
- **Payment Processing Logic**: Updated payment flow to support conditional Stripe integration based on source parameter
  - Modified UnifiedPaymentService.createMultiMethodPayment() to check source parameter before Stripe session creation
  - Enhanced payment logging to distinguish between immigration and non-immigration payment processing
  - Updated payment controller documentation to include source parameter usage and immigration-specific behavior
  - Maintained existing payment functionality for all non-immigration sources without any breaking changes

- **Database Schema Enhancement**: Added website visibility control to immigration services
  - Added website_visible Boolean column to immigration_service table with NOT NULL constraint and default true value
  - Applied proper database migration with rollback capability and zero-downtime deployment
  - Enhanced Prisma schema to include new website_visible field with appropriate default value
  - Regenerated Prisma client to support new schema changes with proper TypeScript type definitions

### Technical Improvements
- **Code Quality and Testing**: Maintained comprehensive testing and build verification
  - Verified TypeScript compilation with npm run build - zero errors after schema changes
  - Confirmed development server startup with npm run start:dev - all routes mapped successfully
  - Validated database migration application with proper Prisma client regeneration
  - Ensured backward compatibility for all existing payment and immigration service functionality

## [4.8.0] - 2025-07-03

### Added
- **Enhanced User Registration System**: Comprehensive enhancement of POST /user/register endpoint with optional password and mobile number support
  - Added optional password handling: Users can now register without providing a password field, creating accounts with null password for third-party authentication flows
  - Added mobile_no field support: New nullable mobile_no column in user table with proper validation using international phone number format regex
  - Enhanced CreateUserDto and UpdateUserDto with optional mobile_no field and comprehensive validation rules
  - Backward compatibility maintained: All existing registration flows continue to work without modification
  - Comprehensive error handling with user-friendly messages for validation failures and authentication scenarios

### Enhanced
- **Database Schema Enhancement**: Added mobile_no column to user table with proper migration support
  - Added nullable mobile_no String field to user Prisma schema for consistent mobile number storage
  - Applied database migration (20250703173407_add_mobile_no_to_user) with automatic Prisma client regeneration
  - Consistent with existing guest model schemas that already include mobile_no fields
  - Zero-downtime migration with proper rollback capability

- **User Service Authentication Logic**: Enhanced user registration and validation with flexible password handling
  - Modified create() method to conditionally hash passwords only when provided, setting null for password-less registrations
  - Enhanced validateUser() method already handles null passwords appropriately with informative error messages for third-party auth users
  - Maintained existing JWT token generation and OTP verification flows without modification
  - Proper error handling for users attempting to login without passwords, directing them to third-party authentication

- **Conditional OTP Email Verification**: Enhanced registration flow to skip email verification for password-less registrations
  - Password-less registrations (dto.password is null/undefined) skip OTP email verification entirely
  - Traditional password registrations maintain existing OTP email verification flow unchanged
  - Different response formats: password registrations return OTP token, password-less return user data with success message
  - Optimized user experience for third-party authentication flows that don't require immediate email verification
  - Maintains backward compatibility with all existing registration scenarios

### Changed
- **User DTO Validation Enhancement**: Updated user data transfer objects with mobile number validation
  - Enhanced CreateUserDto with optional mobile_no field using international phone number regex validation
  - Updated UpdateUserDto to include mobile_no field for profile updates with same validation rules
  - Added comprehensive validation messages for mobile number format requirements
  - Maintained all existing validation rules while adding new optional fields

- **Registration Flow Flexibility**: Enhanced registration endpoint to support multiple authentication scenarios
  - Registration with password + mobile number (traditional flow with OTP verification)
  - Registration without password but with mobile number (third-party auth preparation, no OTP)
  - Registration with password but without mobile number (minimal traditional flow with OTP verification)
  - Registration without both password and mobile number (minimal third-party auth flow, no OTP)
  - Conditional OTP email verification based on password presence for optimized user experience

### Technical Details
- **Database Migration**: Successfully applied migration 20250703173407_add_mobile_no_to_user
- **Validation Regex**: Mobile numbers validated using pattern `^[\+]?[1-9][\d]{0,15}$` supporting international formats
- **Backward Compatibility**: 100% backward compatibility maintained with existing user registration and authentication flows
- **Error Handling**: Enhanced error messages for password-less login attempts directing users to appropriate authentication methods
- **Build Verification**: Confirmed zero build errors with `npm run build` and successful development server startup with `npm run start:dev`

### Testing
- **Comprehensive Test Suite**: Created extensive test coverage for all new registration scenarios
  - Unit tests for UserService covering all password/mobile number combinations and OTP conditional logic
  - Unit tests for UserController ensuring proper endpoint behavior for both response formats
  - Integration tests for complete HTTP request-to-database flow validation with OTP verification scenarios
  - DTO validation tests for mobile number format requirements
  - Error handling tests for edge cases and validation failures
  - Specific tests verifying OTP service is called only for password registrations
  - Test configuration optimized for user module with 95%+ coverage targets

## [4.7.0] - 2025-06-25

### Added
- **numberOfSteps Field Enhancement**: Enhanced GET /applications/{id} endpoint with `numberOfSteps` field
  - Added numberOfSteps field to ApplicationDetailsResponse interface for type safety
  - Integrated calculateNumberOfSteps method in transformApplicationDetails to match GET /applications endpoint structure
  - Consistent workflow step count calculation across both list and detail endpoints
  - Proper error handling with fallback to 0 when workflow template is invalid or missing
  - Non-destructive implementation preserving all existing response fields and functionality

### Enhanced
- **Current Step Validation Logic**: Enhanced PUT /applications/{id}/current-step endpoint with workflow validation
  - Added validation to prevent updating current_step when total workflow steps > requested current_step value
  - Enhanced updateCurrentStep method to fetch and validate against workflow template length
  - Comprehensive error handling with BadRequestException for invalid step updates
  - Detailed error messages indicating total steps vs requested step for better user experience
  - Maintains backward compatibility while adding robust validation logic

### Changed
- **Application Service Enhancement**: Modified updateCurrentStep method with workflow template validation
  - Enhanced database query to include workflow_template.workflowTemplate for validation
  - Added step count validation logic with proper integer parsing and bounds checking
  - Improved error messaging with specific details about validation failures
  - Maintains existing functionality while adding comprehensive validation layer

- **Application Transformer Service**: Enhanced transformApplicationDetails method with numberOfSteps calculation
  - Added numberOfSteps field calculation using existing calculateNumberOfSteps method
  - Updated ApplicationDetailsResponse interface to include numberOfSteps field
  - Consistent data structure between list and detail endpoints for better frontend integration
  - Maintains all existing transformation logic while adding new step count functionality

## [4.6.0] - 2025-06-25

### Added
- **Application Service Name Resolution**: Enhanced GET /applications/{id} endpoint with `service_name` field
  - New `service_name` field in ApplicationDetailsResponse showing resolved service names
  - Automatic resolution from service_type + service_id to actual service names from respective tables
  - Supports all service types: immigration_service, training, packages, service
  - Graceful fallback to generic service name when service not found or resolution fails
  - Non-destructive implementation preserving all existing response fields and functionality

- **Current Step Update Endpoint**: New PUT /applications/{id}/current-step endpoint for workflow step management
  - RESTful endpoint accepting `currentStep` in request body
  - Updates only the `current_step` field in application table with audit logging
  - Comprehensive JWT role-based authentication (User, Admin, Agent access control)
  - Proper validation with user-friendly error messages and HTTP status codes
  - Access control: Users can update own applications, Agents can update assigned applications, Admins have full access
  - Enhanced error handling with detailed logging and rollback mechanisms

### Enhanced
- **Form Data Ordering**: Verified and maintained custom form data ordering by created_at ascending (oldest first)
  - Confirmed existing implementation in getApplicationById method uses correct orderBy clause
  - All form data responses consistently ordered by creation timestamp as per user preference
  - Additional ordering by stage_order and field_name for structured form field presentation

### Changed
- **Application Service Enhancement**: Added service name resolution method
  - New `resolveServiceName` method mapping service_type + service_id to actual service names
  - Enhanced `getApplicationById` method to include resolved service names in response
  - Comprehensive error handling with logging for failed service name resolution
  - Maintains backward compatibility with existing application data structure

- **Application Transformer Service**: Enhanced ApplicationDetailsResponse interface and transformation
  - Added service_name field to ApplicationDetailsResponse interface
  - Updated `transformApplicationDetails` method to include service name with fallback logic
  - Maintains all existing transformation logic while adding new service name functionality

- **Application Controller**: Added new PUT endpoint with comprehensive validation and security
  - New `updateCurrentStep` method with proper JWT authentication guards
  - Role-based access validation ensuring users can only update authorized applications
  - Enhanced error handling with user-friendly messages and appropriate HTTP status codes
  - Comprehensive API documentation with Swagger annotations

### Technical Details
- **Service Resolution Logic**: Robust service name resolution across multiple service types
  - Table mapping: immigration -> immigration_service, service -> service, package -> packages, training -> training
  - Handles missing service IDs and unknown service types gracefully
  - Returns descriptive fallback names when resolution fails
  - Comprehensive logging for debugging and monitoring service resolution issues

- **Database Operations**: Optimized current step updates with proper audit trails
  - Single database update operation for current_step field with updated_at timestamp
  - Application existence validation before update operations
  - Proper error handling for database constraint violations and connection issues
  - Maintains data integrity with transactional update patterns

- **Authentication & Authorization**: Enhanced JWT-based access control
  - Multi-role JWT validation supporting User, Admin, and Agent token types
  - Application-specific access validation ensuring users can only access authorized data
  - Agent assignment validation for role-based application access
  - Comprehensive error responses for unauthorized access attempts

### API Changes
- **GET /applications/{id}**: Enhanced response now includes service_name field
  - New service_name field showing resolved service name (e.g., "Express Entry - Federal Skilled Worker")
  - Field always present with string value (fallback to generic name when resolution fails)
  - No breaking changes to existing response structure or field ordering

- **PUT /applications/{id}/current-step**: New endpoint for workflow step management
  - Request body: `{ "currentStep": "2" }` (string value for workflow step)
  - Response: `{ "success": true, "message": "Current step updated successfully", "applicationId": "...", "currentStep": "2" }`
  - HTTP 200 on success, HTTP 403 for access denied, HTTP 404 for application not found
  - Comprehensive error responses with descriptive messages for client-side handling

## [4.5.0] - 2025-06-23

### Added
- **Application Workflow Steps Count**: Enhanced GET /applications endpoint with `numberOfSteps` field
  - New `numberOfSteps` field in ApplicationListItem interface showing total workflow steps
  - Automatic calculation from workflow template JSON structure (workflowTemplate array length)
  - Graceful error handling for missing or malformed workflow templates (returns 0)
  - Enhanced database query to include workflow template data for step count calculation
  - Consistent camelCase naming convention following API standards
  - Non-destructive implementation preserving all existing functionality

### Changed
- **Application Service Enhancement**: Modified `getApplicationsWithTransformation` method to include workflow template data
  - Added workflow_template include with id, name, description, and workflowTemplate fields
  - Enhanced data retrieval for step count calculation without performance impact
  - Maintained existing query structure and pagination functionality
- **Application Transformer Service**: Enhanced `transformApplicationListItem` method with step count calculation
  - New `calculateNumberOfSteps` private method for workflow template processing
  - Added numberOfSteps field to ApplicationListItem interface
  - Comprehensive error handling with logging for failed step calculations
  - Backward compatibility maintained for applications without workflow templates

### Technical Details
- **Database Query Enhancement**: Added workflow_template relationship to application list queries
  - Includes essential workflow template fields (id, name, description, workflowTemplate)
  - No impact on existing query performance or pagination
  - Maintains role-based access control for Users, Admins, and Agents
- **Step Calculation Logic**: Robust calculation method for workflow template steps
  - Validates workflowTemplate is array before counting length
  - Returns 0 for null, undefined, or malformed workflow templates
  - Logs warnings for calculation failures without breaking API responses
- **Interface Updates**: Enhanced ApplicationListItem interface with numberOfSteps field
  - Type: number (integer representing total workflow steps)
  - Always present in response (0 when no workflow template or calculation fails)
  - Follows existing field naming conventions (camelCase)

### API Changes
- **GET /applications**: Enhanced response now includes numberOfSteps field for each application
  - New field shows total number of steps/stages in application's workflow template
  - Field always present with integer value (0 when no workflow template)
  - No breaking changes to existing response structure
  - Maintains all existing filtering, pagination, and role-based access controls
- **Role-Based Access**: numberOfSteps field respects existing access control patterns
  - Users see numberOfSteps only for their own applications
  - Admins see numberOfSteps for all applications
  - Agents see numberOfSteps only for assigned applications

### Development
- **Build Status**: ✅ npm run build completes successfully with no TypeScript errors
- **Server Status**: ✅ Development server starts successfully on http://localhost:4242
- **Code Quality**: ✅ All changes follow non-destructive development patterns
- **Error Handling**: ✅ Comprehensive error handling with graceful degradation
- **Backward Compatibility**: ✅ All existing functionality preserved
- **Testing Ready**: ✅ Implementation ready for endpoint testing and validation

## [4.4.0] - 2025-06-23

### Added
- **Application Notes Field**: Added `note` field to application table for internal use
  - New database field `note` in application schema with optional string type
  - Updated GET `/applications/{id}` endpoint to include note field in response
  - New PUT `/applications/{id}/note` endpoint for updating application notes
  - Proper validation with 2000 character limit and error handling
  - Role-based access control for users, agents, and admins
  - Agent access restricted to assigned applications only

- **Enhanced Document Vault Endpoint**: Improved document vault functionality with pagination
  - Enhanced GET `/documents` endpoint with comprehensive pagination support
  - Added structured response format with metadata (total, page, limit, totalPages)
  - Improved filtering by document type and search functionality
  - Backward compatibility maintained with legacy methods
  - Proper authentication middleware for secure access
  - Comprehensive error handling and validation

### Changed
- Updated `ApplicationDetailsResponseDto` to include optional note field
- Enhanced `DocumentVaultService` with pagination support and legacy compatibility
- Improved document controller with structured response format and better error handling

### Technical Details
- Database migration: `add_note_field_to_application` successfully applied
- New DTOs: `UpdateApplicationNoteDto`, `UpdateApplicationNoteResponseDto`, `DocumentVaultQueryDto`, `DocumentVaultResponseDto`
- Enhanced interfaces with proper typing for pagination responses
- Maintained backward compatibility for all existing functionality
- All TypeScript compilation successful with no errors - 2025-06-22

### Added
- **Workflow Template Duplicate Prevention** - Enhanced duplicate prevention system for workflow templates
  - Service-based duplicate prevention ensuring only one active workflow template per immigration package
  - Validation logic checks for existing active templates with same serviceType and serviceId combination
  - Comprehensive error handling with detailed conflict messages including existing template information
  - Support for both specific service IDs and general service types (null serviceId)
  - Enhanced create and update methods with proper duplicate validation
- **Application Estimated Completion Management** - New endpoint for updating estimated completion dates
  - `PUT /applications/{applicationId}/estimated-completion` - Admin/Agent only endpoint for updating estimated completion dates
  - Comprehensive date validation (future dates only, proper ISO 8601 format)
  - Role-based access control with JwtAdminOrAgent guard
  - Complete error handling and logging
- **Enhanced Application List Response** - Estimated completion field now included in GET /applications response
  - ApplicationListItem interface updated to include estimated_completion field
  - Transformer service enhanced to serialize estimated completion dates
  - Consistent date formatting across all application endpoints
- **Document Ordering Enhancement** - Confirmed GET /applications/{id} returns documents ordered by updated_at descending
  - Most recently updated documents appear first in application details
  - Follows user's preferred pattern for document updates sorting
- **Comprehensive DTO Validation** - New DTOs for estimated completion updates
  - UpdateEstimatedCompletionDto with proper date string validation
  - UpdateEstimatedCompletionResponseDto for consistent API responses
  - Full Swagger/OpenAPI documentation for new endpoints

### Changed
- **Workflow Template Service Enhancement** - Enhanced duplicate prevention logic for workflow templates
  - New checkServiceDuplicate method for validating active template uniqueness
  - Enhanced create method with service-based duplicate validation before template creation
  - Enhanced update method with duplicate validation for serviceType, serviceId, and isActive changes
  - Intelligent validation that only checks duplicates when templates will be active after operation
  - Detailed logging for all duplicate prevention operations with correlation IDs
- **Application Service Enhancement** - Added updateEstimatedCompletion method with comprehensive validation
  - Application existence validation before updates
  - Date parsing and validation with proper error messages
  - Future date requirement enforcement
  - Detailed logging for audit trails
- **Application Transformer Service** - Enhanced to include estimated completion in list responses
  - ApplicationListItem interface extended with estimated_completion field
  - Consistent date serialization using toISOString() format
  - Backward compatibility maintained for existing responses

### Technical Details
- **Workflow Template Duplicate Prevention**: Advanced validation system for ensuring template uniqueness
  - Database queries check for existing active templates with same serviceType and serviceId combination
  - Supports both specific service IDs and general service types (null serviceId handling)
  - Excludes current template from duplicate checks during updates to prevent false positives
  - Only validates duplicates when templates will be active after the operation
  - Comprehensive error messages include existing template details (name and ID) for better user experience
- **Authentication**: Uses existing JwtAdminOrAgent guard for proper role-based access control
- **Validation**: Comprehensive input validation with class-validator decorators
- **Error Handling**: Proper HTTP status codes and meaningful error messages
- **Database**: Utilizes existing estimated_completion field in application schema
- **Logging**: Detailed logging for all operations with correlation IDs
- **Documentation**: Complete Swagger documentation for new endpoints

### API Changes
- **Workflow Template Endpoints** - Enhanced duplicate prevention for workflow template operations
  - `POST /workflow-templates` - Now validates for duplicate active templates with same serviceType/serviceId
  - `PATCH /workflow-templates/{id}` - Validates duplicates when updating serviceType, serviceId, or activating templates
  - Error responses include detailed conflict information with existing template details
  - HTTP 409 Conflict status returned when duplicate active templates would be created
  - No breaking changes to existing request/response structures
- `PUT /applications/{applicationId}/estimated-completion` - New endpoint for updating estimated completion dates
  - Request body: `{ "estimated_completion": "2025-07-15T10:30:00.000Z" }`
  - Response: Complete application details with updated estimated completion
  - Requires Admin or Agent authentication
- `GET /applications` - Enhanced response now includes estimated_completion field
  - No breaking changes to existing response structure
  - Additional field provides estimated completion dates in list view
- `GET /applications/{id}` - Confirmed documents ordered by updated_at descending (newest first)
  - Existing functionality verified and documented
  - Follows established user preference for document ordering

### Development
- **Workflow Template Testing**: ✅ Comprehensive test suite for duplicate prevention functionality
  - 29 total tests passing (27 existing + 2 new duplicate prevention tests)
  - Service-based duplicate prevention tests for create and update operations
  - Edge case testing for inactive templates, different service types, and null service IDs
  - Mock-based testing with proper test isolation and data management
- **Server Status**: ✅ Development server starts successfully with 0 compilation errors
- **Build Status**: ✅ npm run build completes successfully with no TypeScript errors
- **Code Quality**: ✅ All new code follows established patterns and conventions
- **Non-Destructive**: ✅ All changes follow non-destructive development patterns
- **Testing Ready**: ✅ Implementation ready for comprehensive testing with different user roles

## [Previous Release] - 2025-06-18

### Added
- **Universal Authentication Module** - New `/auth` module for role-based frontend functionality
- **JWT Token Type Endpoint** - New `GET /auth/profile` endpoint returning user token type from JWT payload
- **Frontend Role-Based Examples** - Comprehensive React examples in `frontend-examples/` directory
  - Authentication context with state management
  - Role-based UI components and guards
  - Protected routing patterns
  - Complete implementation guide and documentation
- **Simplified JWT Architecture** - Frontend treats tokens as opaque strings, backend determines roles

### Changed
- **Auth Endpoint Simplification** - `/auth/profile` now returns minimal response `{ tokenType: string }`
- **JWT Token Handling** - Removed complex database queries from token type retrieval
- **Performance Optimization** - Eliminated unnecessary database dependencies from auth endpoints

### Removed
- **Auth Service Dependencies** - Removed `src/auth/auth.service.ts` with problematic database schema references
- **Complex Profile Queries** - Removed database-heavy profile data fetching from auth endpoints

### Fixed
- **Compilation Errors** - Resolved TypeScript errors related to non-existent database fields (`phone`, `department`)
- **Server Startup Issues** - Fixed development server startup problems caused by Prisma schema mismatches
- **JWT Architecture** - Properly aligned JWT token handling between frontend and backend

### Technical Details
- **Security**: Token verification remains server-side with existing multi-secret system
- **Compatibility**: Works with existing JWT guards (JwtAdmin, JwtAgent, JwtUser, JwtGuard)
- **Performance**: Eliminated database queries for simple token type retrieval
- **Frontend Integration**: Documented proper patterns for role-based UI without client-side JWT decryption

### API Changes
- `GET /auth/profile` - Returns `{ tokenType: "user|admin|agent|mentor" }` from JWT payload
- Endpoint protected by JwtGuard and works for all user types
- No breaking changes to existing authentication system

### Development
- **Server Status**: ✅ Development server starts successfully with 0 compilation errors
- **Module Loading**: ✅ All modules including new AuthModule load correctly
- **Route Mapping**: ✅ New `/auth/profile` endpoint properly mapped
- **Error Resolution**: ✅ All TypeScript compilation issues resolved

## [Previous Release] - 2025-06-08

### Removed
- **GDPR Security Implementation** - Complete removal of GDPR compliance and security implementation components
- **Security Module** - Removed entire security module (7 files) including SecurityService, GDPRService, and DataRetentionService
- **Application Access Guard** - Removed GDPR-specific access control guard
- **Security Test Suite** - Removed all security-related test files (4 files)
- **Database Security Schema** - Removed security.prisma schema and GDPR migration files
- **Security Documentation** - Removed GDPR implementation and security log documentation

### Changed
- **Application Module** - Updated app.module.ts to remove SecurityModule import
- **System Architecture** - Simplified architecture by removing GDPR compliance layer
- **Codebase Size** - Reduced codebase by 5,479 lines while maintaining core functionality

### Technical Details
- **Files Removed**: 19 total files (7 security modules, 4 test files, 2 database files, 2 documentation files, 4 other files)
- **Core Functionality Preserved**: Payment processing, user management, dashboard, immigration services remain fully functional
- **No Breaking Changes**: All core business logic and APIs continue to work as expected
- **Clean Removal**: No orphaned imports or dependencies remain

### Migration Impact
- **Database**: GDPR-related tables removed (security_log, gdpr_request, data_retention_policy)
- **API Endpoints**: Security and GDPR endpoints no longer available
- **Authentication**: Core JWT authentication and authorization systems unchanged
- **Document Security**: Document permission system (DocumentSecurityService) preserved as it's not GDPR-related

## [Previous Release] - 2025-05-31

### Added
- **Admin Unified Payment Integration** - Added admin-specific endpoints to unified payment controller
- **Unified Admin Payment Endpoints** - 3 new admin endpoints replacing 8 legacy progress update endpoints
- **Admin Payment Progress Update** - Single endpoint (`PATCH /v2/payment/admin/progress`) for all service types
- **Admin Payment Test Suite** - Complete test coverage for admin payment functionality (18 tests passing)
- **Payment Integration Tests** - Added comprehensive integration test suite for Payment table operations with mocked database
- **Payment CRUD Testing** - Complete test coverage for payment creation, reading, updating, and deletion operations
- **Payment Analytics Testing** - Test suite for payment aggregations, revenue calculations, and reporting features
- **Service Integration Testing** - End-to-end testing for UnifiedPaymentService with mocked Prisma operations
- **Multi-Service Payment Testing** - Tests for all service types (service, package, immigration, training)
- **Guest Payment Testing** - Comprehensive testing for guest payment flows without authentication

### Testing Infrastructure
- **Payment Test Suite** - Comprehensive test organization with feature-based folder structure (test/payment/)
- **Mocked Integration Framework** - Reliable testing with mocked Prisma operations for consistent results
- **Mock Data Management** - Extensive test fixtures for payment scenarios and edge cases
- **Test Performance Optimization** - Fast test execution with mocked dependencies (3.666s for 60 tests)
- **Test Data Isolation** - Proper test isolation with mock resets between test cases
- **Database Schema Testing** - Validation of foreign key relationships and data integrity with mocks

### Code Quality Improvements
- **100% Test Pass Rate** - All 60 tests passing successfully (3 test suites, 0 failures)
- **Enhanced Test Coverage** - 60 total tests covering all payment functionality:
  - 48 unit tests (controller + service)
  - 7 integration tests (mocked database operations)
  - 5 additional integration scenarios
- **Error Handling Tests** - Comprehensive testing of error scenarios and edge cases
- **Concurrent Operation Tests** - Testing for race conditions and concurrent payment processing
- **Validation Testing** - Input validation and business rule enforcement testing
- **Legacy Test Cleanup** - Removed unused test directories (test/database, test/logs) and legacy mock files

### Test Results Summary
- ✅ **3 test suites passed** (unified-payment.controller.spec.ts, unified-payment.service.spec.ts, payment.integration.spec.ts)
- ✅ **60 tests passed** with 100% success rate
- ✅ **0 failed tests** - Complete reliability
- ✅ **Fast execution** - 3.666 seconds total runtime
- ✅ **Mocked database operations** - No external dependencies required
- ✅ **Comprehensive coverage** - All payment table operations tested

### Technical Improvements
- **Comprehensive Code Documentation** - Added detailed JSDoc comments to all payment-related code
- **Unified Payment System** - Implemented new unified payment architecture to consolidate payment tables
- **Payment Code Comments** - Added extensive documentation for payment service methods, controllers, and DTOs
- **Developer Documentation** - Enhanced code readability with method descriptions, parameter documentation, and usage examples
- **Payment Service Documentation** - Added comprehensive method-level documentation for all payment operations
- **Payment Controller Documentation** - Added endpoint documentation with parameter descriptions and usage examples
- **Unified Payment Service Documentation** - Added detailed documentation for the new unified payment architecture
- **Payment DTO Documentation** - Added validation and property descriptions for all payment data transfer objects
- **Payment Module Documentation** - Added module-level documentation explaining dependencies and features

## [0.0.1] - 2025-03-27

### Added
- **Blog Comments System** - Complete commenting functionality for blog posts
- **Customer Review System** - Customer review management with ordering capabilities
- **Training Module** - Training programs with image support and ordering
- **Guest Purchase System** - Non-authenticated purchase flow for all services
- **Payment Integration** - Stripe payment processing for multiple service types
- **Email Notification System** - Automated email notifications for purchases and admin alerts
- **Admin Dashboard** - Administrative interface for managing users, mentors, and services
- **User Authentication** - JWT-based authentication with refresh token support
- **Mentor Management** - Mentor profile management with service offerings
- **Immigration Services** - Immigration consultation service management
- **Package Management** - Service package creation and management
- **Media Upload** - File upload functionality with Supabase integration
- **Resume Builder** - Resume creation and management tools
- **Contact Us System** - Contact form and inquiry management
- **OTP Verification** - Email-based OTP verification system
- **Password Management** - Password reset and change functionality

### Technical Features
- **NestJS Framework** - Built on NestJS with TypeScript
- **Prisma ORM** - Database management with Prisma
- **Fastify Platform** - High-performance HTTP server
- **Swagger Documentation** - API documentation with OpenAPI
- **JWT Guards** - Role-based access control (User, Admin, Mentor)
- **Email Templates** - React-based email templates
- **Database Migrations** - Comprehensive database schema management
- **Error Handling** - Global exception filters
- **File Processing** - PDF and document processing capabilities
- **OpenAI Integration** - AI-powered features for resume building

### Database Schema
- **User Management** - User profiles with authentication
- **Mentor System** - Mentor profiles and service offerings
- **Service Management** - Various service types (mentor, immigration, training)
- **Package System** - Service packages and pricing
- **Payment Tracking** - Both authenticated and guest payment records
- **Blog System** - Blog posts with comments
- **Review System** - Customer reviews with ratings
- **Contact Management** - Contact inquiries and responses

### API Endpoints
- **Authentication** - Login, register, refresh token, OTP verification
- **User Management** - Profile management, password reset
- **Mentor Services** - Mentor CRUD operations and service management
- **Payment Processing** - Stripe integration for all service types
- **Guest Services** - Non-authenticated service purchases
- **Admin Panel** - Administrative operations
- **Blog Management** - Blog posts and comments
- **Media Handling** - File upload and management
- **Dashboard** - User and admin dashboards

### Security Features
- **JWT Authentication** - Secure token-based authentication
- **Role-based Access** - Different access levels for users, mentors, and admins
- **Password Encryption** - bcrypt password hashing
- **Input Validation** - Comprehensive request validation
- **CORS Configuration** - Cross-origin resource sharing setup

### Payment System
- **Stripe Integration** - Complete payment processing with Checkout Sessions
- **Multiple Service Types** - Support for mentor, package, immigration, and training services
- **Guest Payments** - Non-authenticated payment flow for all service types
- **Webhook Handling** - Automated payment confirmation and status updates
- **Email Notifications** - Purchase confirmations and admin alerts with React templates
- **Unified Payment Architecture** - New consolidated payment table structure (v2.0)
- **Legacy Payment Support** - Backward compatibility with existing payment tables
- **Payment Analytics** - Revenue tracking and reporting capabilities
- **Payment Status Management** - Comprehensive payment lifecycle tracking

### Email System
- **Transactional Emails** - Purchase confirmations, OTP verification
- **Admin Notifications** - New purchase and inquiry alerts
- **Template System** - React-based email templates
- **Multiple Providers** - Support for various email services

### Development Tools
- **TypeScript** - Full TypeScript implementation
- **ESLint & Prettier** - Code formatting and linting
- **Jest Testing** - Unit and integration testing setup
- **Docker Support** - Containerization configuration
- **Environment Configuration** - Comprehensive environment variable management

### Infrastructure
- **Supabase Integration** - Database and storage backend
- **Stripe Payment Gateway** - Payment processing
- **Email Service Integration** - Transactional email delivery
- **File Storage** - Media and document storage
- **OpenAI API** - AI-powered features

## Version History

### Recent Updates (2025)
- **2025-03-27**: Blog comments system implementation
- **2025-03-25**: Package reordering fixes
- **2025-03-21**: Service reordering functionality
- **2025-03-13**: Customer review system
- **2025-03-05**: Admin email notifications
- **2025-02-27**: Training email template updates
- **2025-02-24**: Training API improvements
- **2025-02-21**: User profile enhancements
- **2025-02-18**: Training module implementation
- **2025-02-10**: Email template improvements
- **2025-02-07**: Purchase notification templates
- **2025-02-06**: Mentor schema updates
- **2025-02-05**: Email image support
- **2025-02-03**: User validation improvements
- **2025-01-31**: Guest purchase history API
- **2025-01-30**: User detail API fixes and delete functionality
- **2025-01-29**: Guest purchase system
- **2025-01-24**: Training system implementation
- **2025-01-22**: Password management and account deletion
- **2025-01-18**: Credential login fixes
- **2025-01-17**: Email system implementation
- **2025-01-16**: Refresh token API fixes
- **2025-01-15**: Dashboard API and progress tracking
- **2025-01-07**: User profile API updates

### Foundation (2024)
- **2024-12-27**: Payment method implementation
- **2024-12-26**: Package and immigration services
- **2024-12-16**: Admin user and mentor management
- **2024-12-13**: JWT decoding fixes
- **2024-12-12**: User detail retrieval
- **2024-12-11**: Review system implementation
- **2024-12-10**: Core services (blog, mentor, service, contact)
- **2024-11-26**: Mentor API and schema updates
- **2024-07-11**: Project initialization and AI prompt setup
- **2024-06-27**: ChatGPT model integration

## Dependencies

### Core Dependencies
- **@nestjs/core**: ^10.0.0 - NestJS framework
- **@prisma/client**: ^6.5.0 - Database ORM
- **stripe**: ^17.4.0 - Payment processing
- **@supabase/supabase-js**: ^2.43.1 - Backend services
- **bcrypt**: ^5.1.1 - Password hashing
- **@nestjs/jwt**: ^10.2.0 - JWT authentication
- **openai**: ^4.47.1 - AI integration
- **nodemailer**: ^6.9.13 - Email sending

### Development Dependencies
- **typescript**: ^5.1.3 - TypeScript compiler
- **@nestjs/testing**: ^10.0.0 - Testing utilities
- **jest**: ^29.5.0 - Testing framework
- **eslint**: ^8.42.0 - Code linting
- **prettier**: ^3.0.0 - Code formatting

## Migration Plans

### Payment Table Consolidation (In Progress)
- **Current State**: 8 separate payment tables (user_mentor_service, guest_mentor_service, user_package, guest_package, user_immigration_service, guest_immigration_service, user_training, guest_training)
- **Target State**: Single unified payment table
- **Progress**:
  - ✅ Unified payment table schema created
  - ✅ UnifiedPaymentService implemented with full CRUD operations
  - ✅ UnifiedPaymentController implemented with v2 API endpoints
  - ✅ Revenue analytics and reporting capabilities added
  - ✅ Email notification system integrated
  - ✅ Backward compatibility layer implemented
  - ⏳ Legacy payment service migration (pending)
  - ⏳ Production data migration (pending)
- **Migration Strategy**: See PAYMENT_MIGRATION.md for detailed plan
- **Documentation**: Comprehensive code comments added for developer reference

### User Role Unification (Planned)
- **Current State**: 3 separate user tables (user, admin, mentor) with different authentication systems
- **Target State**: Single unified user table with role-based access control supporting 4 user types (user, admin, mentor, agent)
- **New Features**: Immigration agent role with specialized permissions and profile fields
- **Migration Strategy**: See USER_ROLE_MIGRATION.md for detailed plan

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under UNLICENSED.
